const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-ts");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const Dotenv = require("dotenv-webpack");

const varAmbienteLocal = require("./src/environments/environment.local.js");
const varAmbienteDev = require("./src/environments/environment.dev.js");
const varAmbienteHm = require("./src/environments/environment.hm.js");
const varAmbienteProd = require("./src/environments/environment.prd.js");
const CopyWebpackPlugin = require("copy-webpack-plugin");

module.exports = (webpackConfigEnv, argv) => {
  const orgName = "caixa";
  const defaultConfig = singleSpaDefaults({
    orgName,
    projectName: "sipnc-host",
    webpackConfigEnv,
    argv,
    disableHtmlGeneration: true,
  });
  let varEnvPath;
  let varAmbiente;
  if (webpackConfigEnv.env === "dev") {
    varEnvPath = "src/environments/dev.env";
    varAmbiente = varAmbienteDev;
  }

  if (webpackConfigEnv.env === "hm") {
    varEnvPath = "src/environments/hm.env";
    varAmbiente = varAmbienteHm;
  }

  if (webpackConfigEnv.env === "prd") {
    varEnvPath = "src/environments/prod.env";
    varAmbiente = varAmbienteProd;
  }
  if (webpackConfigEnv.env === "local") {
    varEnvPath = "src/environments/local.env";
    varAmbiente = varAmbienteLocal;
  }

  return merge(defaultConfig, {
    // Configuração otimizada para evitar re-renderização completa
    devServer: {
      hot: true,
      liveReload: false, // ← LINHA MAIS IMPORTANTE: Evita reload completo da página
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      // Configuração de proxy para resolver CORS com MFEs
      proxy: {
        '/mfe-proxy/previdencia/*': {
          target: 'http://localhost:4002',
          pathRewrite: { '^/mfe-proxy/previdencia': '' },
          changeOrigin: true,
          logLevel: 'debug'
        },
        '/mfe-proxy/vida/*': {
          target: 'http://localhost:4001',
          pathRewrite: { '^/mfe-proxy/vida': '' },
          changeOrigin: true,
          logLevel: 'debug'
        },
        '/mfe-proxy/prestamista/*': {
          target: 'http://localhost:4003',
          pathRewrite: { '^/mfe-proxy/prestamista': '' },
          changeOrigin: true,
          logLevel: 'debug'
        }
      }
    },
    plugins: [
      new Dotenv({
        path: varEnvPath,
      }),
      new HtmlWebpackPlugin({
        inject: false,
        template: "src/index.ejs",
        publicPath: "public",
        favicon: "public/favicon.ico",
        templateParameters: {
          varAmbiente: varAmbiente,
        },
      }),
      new CopyWebpackPlugin({
        patterns: [{ from: "static", to: "static" }],
      }),
    ],
  });
};
