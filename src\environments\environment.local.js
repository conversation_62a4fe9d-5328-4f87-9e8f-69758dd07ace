/* eslint-disable prettier/prettier */
//Desenvolvedor: EDITAR ESTE ARQUIVO APENAS caso você esteja desenvolvendo MFEs.
//O seu MFE deve ser adicionado na variável "listaMFEs" abaixo.
//NÃO é necessário editar a variável "variaveisAmbiente".
//Após editar este arquivo, é necessário que o comando npm start seja executado novamente,
//pois a atualização automática de alterações NÃO funciona para este caso.
const common = require("./environment.common");
const listaMFEs = `[
  /*{
    "application":"PlataformaCaixa-PosVenda-Vida",
    "route":"/seguridade/vida/pos-venda",
    "path":"http://localhost:9000/mfe-proxy/vida/CVP-PlataformaCaixa-PosVenda-Vida.js"
  },
  {
    "application": "PlataformaCaixa-PosVenda-Prestamista",
    "route": "/seguridade/prestamista/pos-venda",
    "path": "http://localhost:9000/mfe-proxy/prestamista/CVP-PlataformaCaixa-PosVenda-Prestamista.js"
  },*/
  {
    "application":"PlataformaCaixa-PosVenda-Previdencia",
    "route":"/seguridade/previdencia/pos-venda",
    "path":"http://localhost:9000/mfe-proxy/previdencia/CVP-PlataformaCaixa-PosVenda-Previdencia.js"
  },
  {
    "application":"plataformaCaixa-Venda-Vida",
    "route":"/seguridade/vida/venda",
    "path":"//localhost:6001/CVP-PlataformaCaixa-Venda-Vida.js"
  },
   {
    "application":"PlataformaCaixa-Venda-Previdencia",
    "route":"/seguridade/previdencia/venda",
    "path":"//localhost:6002/CVP-PlataformaCaixa-Venda-Previdencia.js"
  },
  {
    "application":"PlataformaCaixa-Venda-Prestamista",
    "route":"/seguridade/prestamista/venda",
    "path":"//localhost:6003/CVP-PlataformaCaixa-Venda-Prestamista.js"
  },
  {
    "application":"caixa-sipnc-assinaturas-des",
    "route":"/assinaturas",
    "path":"https://sipnc-assinaturas-microfront-des.apps.nprd.caixa/main.js"
  }
]`;

const variaveisAmbiente = {
  urlCDN: "http://localhost:9000",
  urlHost: "//localhost:9000/caixa-sipnc-host.js",
  urlCore: "http://localhost:9000/static/core/caixa-sipnc-core.js",
  urlNavBar: "http://localhost:9000/static/navbar/main-es5.js",
  urlMenuDinamico: "http://localhost:9000",
  listaMFEs: listaMFEs,
  isBuscarMFEsDinamicos: "false",
  ...common,
};
module.exports = variaveisAmbiente;

/*Legenda:
  urlCDN: endereço do CDN;
  urlHost: endereço do host;
  urlCore: endereço do core; //Editar esta variável quando estiver modificando o core: //localhost:4000;
  urlNavBar: endereço do navbar; //Editar esta variável quando estiver modificando o navbar: //localhost:4201;
  urlMenuDinamico: endereço do backend do menu dinâmico;
  listaMFEs: lista de MFEs a serem carregados pelo host;
  isBuscarMFEsDinamicos: flag que indica se a lista de MFEs a serem carregados virá da variável "listaMFEs" ou
                         do backend do menu dinâmico;
*/
