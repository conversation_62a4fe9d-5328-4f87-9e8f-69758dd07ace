
// Single SPA global interface extension
declare global {
  interface Window {
    singleSpa?: {
      getAppNames(): string[];
      getAppStatuses(): Record<string, string>;
      [key: string]: any;
    };
    System?: {
      import(module: string): Promise<any>;
      getImportMap?(): any;
      [key: string]: any;
    };
  }
}

// This export makes this file an external module, allowing global augmentations
export {};

