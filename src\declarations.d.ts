
// Single SPA global interface extension
declare global {
  interface Window {
    singleSpa?: {
      getAppNames(): string[];
      getAppStatuses?(): Record<string, string>; // Opcional pois pode não existir em todas as versões
      getAppStatus?(appName: string): string; // Método alternativo
      [key: string]: any;
    };
    System?: {
      import(module: string): Promise<any>;
      getImportMap?(): any;
      prepareImport?(flag: boolean): void;
      [key: string]: any;
    };
  }
}

// This export makes this file an external module, allowing global augmentations
export {};

