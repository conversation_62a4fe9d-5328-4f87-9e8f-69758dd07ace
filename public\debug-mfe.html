<!DOCTYPE html>
<html>
<head>
    <title>Debug MFE Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        p { margin: 5px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>🔍 Debug MFE Loading</h1>
    
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            console.log(message);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            results.innerHTML += `<p class="${className}">${message}</p>`;
        }
        
        // Teste 1: Verificar se o MFE está acessível diretamente
        log("🧪 Teste 1: Verificando MFE direto na porta 4002...");
        fetch('http://localhost:4002/CVP-PlataformaCaixa-PosVenda-Previdencia.js')
            .then(response => {
                if (response.ok) {
                    log("✅ MFE acessível diretamente na porta 4002", 'success');
                    return response.text();
                } else {
                    log(`❌ MFE não acessível - Status: ${response.status}`, 'error');
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(content => {
                log(`📄 Tamanho do conteúdo: ${content.length} chars`);
                if (content.includes('mount') && content.includes('unmount')) {
                    log("✅ MFE parece ser válido (contém mount/unmount)", 'success');
                } else {
                    log("⚠️ MFE pode não ser válido (não contém mount/unmount)", 'warning');
                }
            })
            .catch(err => log(`❌ Erro ao acessar MFE direto: ${err.message}`, 'error'));
        
        // Teste 2: Verificar se o proxy está funcionando
        log("🧪 Teste 2: Verificando MFE através do proxy...");
        fetch('http://localhost:9000/mfe-proxy/previdencia/CVP-PlataformaCaixa-PosVenda-Previdencia.js')
            .then(response => {
                if (response.ok) {
                    log("✅ MFE acessível através do proxy", 'success');
                    return response.text();
                } else {
                    log(`❌ MFE não acessível via proxy - Status: ${response.status}`, 'error');
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(content => {
                log(`📄 Tamanho do conteúdo via proxy: ${content.length} chars`);
            })
            .catch(err => log(`❌ Erro ao acessar MFE via proxy: ${err.message}`, 'error'));
        
        // Teste 3: Verificar configuração do ambiente
        log("🧪 Teste 3: Verificando configuração...");
        setTimeout(() => {
            const jsonApps = sessionStorage.getItem("json-apps");
            if (jsonApps) {
                log("✅ SessionStorage json-apps encontrado", 'success');
                try {
                    const apps = JSON.parse(jsonApps);
                    log(`📱 Número de apps: ${apps.length}`);
                    apps.forEach(app => {
                        log(`📋 App: ${app.application} - Rota: ${app.route} - Path: ${app.path}`);
                    });
                } catch (e) {
                    log(`❌ Erro ao parsear json-apps: ${e.message}`, 'error');
                }
            } else {
                log("❌ SessionStorage json-apps não encontrado", 'error');
            }
            
            // Verificar Single SPA
            if (window.singleSpa) {
                log("✅ Single SPA disponível", 'success');
                try {
                    const appNames = window.singleSpa.getAppNames();
                    log(`📱 Apps registrados no Single SPA: ${appNames.join(', ')}`);
                    
                    // Verificar status dos apps
                    if (typeof window.singleSpa.getAppStatuses === 'function') {
                        const statuses = window.singleSpa.getAppStatuses();
                        Object.keys(statuses).forEach(appName => {
                            log(`📊 ${appName}: ${statuses[appName]}`);
                        });
                    }
                } catch (e) {
                    log(`❌ Erro ao obter apps do Single SPA: ${e.message}`, 'error');
                }
            } else {
                log("❌ Single SPA não disponível", 'error');
            }
            
            // Verificar URL atual
            log(`🌐 URL atual: ${window.location.href}`);
            log(`📍 Pathname: ${window.location.pathname}`);
            
            // Verificar elementos DOM
            const previdenciaElement = document.getElementById('single-spa-application:PlataformaCaixa-PosVenda-Previdencia');
            if (previdenciaElement) {
                log("✅ Elemento DOM do MFE encontrado", 'success');
                log(`📏 Dimensões: ${previdenciaElement.offsetWidth}x${previdenciaElement.offsetHeight}`);
                log(`📄 Conteúdo: ${previdenciaElement.innerHTML.length > 0 ? 'Tem conteúdo' : 'Vazio'}`);
            } else {
                log("❌ Elemento DOM do MFE não encontrado", 'error');
            }
            
        }, 3000);
        
        // Teste 4: Simular navegação para a rota do MFE
        setTimeout(() => {
            log("🧪 Teste 4: Simulando navegação para rota do MFE...");
            
            // Criar um link para testar
            const link = document.createElement('a');
            link.href = '/seguridade/previdencia/pos-venda';
            link.textContent = '🔗 Clique aqui para navegar para a rota do MFE';
            link.style.display = 'block';
            link.style.margin = '20px 0';
            link.style.padding = '10px';
            link.style.background = '#007bff';
            link.style.color = 'white';
            link.style.textDecoration = 'none';
            link.style.borderRadius = '5px';
            
            results.appendChild(link);
            
        }, 4000);
    </script>
</body>
</html>
