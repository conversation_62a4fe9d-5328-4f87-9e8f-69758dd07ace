// Mock MFE para testar Single SPA
console.log("🧪 Mock MFE Previdência carregado!");

const lifecycles = {
  mount(props) {
    console.log("🚀 Montando Mock MFE Previdência", props);
    
    // Encontra o elemento DOM onde deve ser montado
    const domElement = document.getElementById('single-spa-application:PlataformaCaixa-PosVenda-Previdencia');
    
    if (domElement) {
      domElement.innerHTML = `
        <div style="padding: 20px; background: #f0f8ff; border: 2px solid #007bff; border-radius: 8px; margin: 20px;">
          <h2 style="color: #007bff; margin: 0 0 15px 0;">🎉 MFE Previdência - Pós Venda</h2>
          <p style="margin: 10px 0;"><strong>Status:</strong> ✅ Carregado com sucesso!</p>
          <p style="margin: 10px 0;"><strong>Rota:</strong> /seguridade/previdencia/pos-venda</p>
          <p style="margin: 10px 0;"><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
          <div style="background: #e7f3ff; padding: 10px; border-radius: 4px; margin-top: 15px;">
            <p style="margin: 0; font-size: 14px;">
              🔧 Este é um MFE mock para testar o Single SPA.<br>
              Se você está vendo esta mensagem, o Single SPA está funcionando corretamente!
            </p>
          </div>
        </div>
      `;
      console.log("✅ Mock MFE Previdência montado no DOM");
    } else {
      console.error("❌ Elemento DOM não encontrado para o MFE Previdência");
    }
    
    return Promise.resolve();
  },

  unmount(props) {
    console.log("🔄 Desmontando Mock MFE Previdência", props);
    
    const domElement = document.getElementById('single-spa-application:PlataformaCaixa-PosVenda-Previdencia');
    if (domElement) {
      domElement.innerHTML = '';
      console.log("✅ Mock MFE Previdência desmontado");
    }
    
    return Promise.resolve();
  }
};

// Exporta os lifecycles para o Single SPA
export const mount = lifecycles.mount;
export const unmount = lifecycles.unmount;

console.log("✅ Mock MFE Previdência - Lifecycles exportados");
