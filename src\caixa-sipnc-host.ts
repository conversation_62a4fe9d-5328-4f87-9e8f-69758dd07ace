
import { registerApplication, start, LifeCycles } from "single-spa";

registerApplication({
    name: "caixa-sipnc-navbar",
    app: () => System.import<LifeCycles>("caixa-sipnc-navbar"),
    activeWhen: () => { return true }
});



let jsonApps;
try {
    const jsonAppsString = sessionStorage.getItem("json-apps");
    if (jsonAppsString) {
        jsonApps = JSON.parse(jsonAppsString);
        console.log("Apps carregados do sessionStorage:", jsonApps);
    } else {
        console.warn("Nenhum app encontrado no sessionStorage com chave 'json-apps'");
        jsonApps = [];
    }
} catch (error) {
    console.error("Erro ao parsear json-apps do sessionStorage:", error);
    jsonApps = [];
}

for (let appl of jsonApps) {
    try {
        registerApplication({
            name: appl.application,
            app: () => System.import<LifeCycles>(appl.application),
            activeWhen: (location) => ativarApp(location, appl)
        });
        console.log(`App registrado: ${appl.application}`);
    } catch (error) {
        console.error(`Erro ao registrar app ${appl.application}:`, error);
    }
}
start();

function ativarApp(location: Location, appl: any) {
    let rotaAppl = appl.route.replace(/\/+$/, '').toLowerCase();
    let rotaSelecionada = location.pathname.replace(/\/+$/, '').toLowerCase();
    if (comparaRotas(rotaAppl, rotaSelecionada)) {
        return true;
    }
    return false;
}

function comparaRotas(rotaApp: string, rotaLocation: string) {
    if (rotaApp == rotaLocation) {
        return true;
    } else {
        if (rotaApp.length >= rotaLocation.length) {
            return false;
        } else {
            if (rotaLocation.startsWith(rotaApp) && rotaLocation.charAt(rotaApp.length) == '/') {
                return true;
            } else {
                return false;
            }
        }
    }
}

// Debug rápido - cole no console:
console.log("SessionStorage:", sessionStorage.getItem("json-apps"));

// Aguarda Single SPA estar disponível antes de fazer debug
setTimeout(() => {
    console.log("Apps registrados:", window.singleSpa?.getAppNames());
    console.log("Status apps:", window.singleSpa?.getAppStatuses());

    // Debug adicional
    if (window.singleSpa) {
        console.log("Single SPA disponível:", !!window.singleSpa);
        console.log("Número de apps:", window.singleSpa.getAppNames()?.length || 0);
    } else {
        console.warn("Single SPA não está disponível no window object");
    }
}, 1000);

