
import { registerApplication, start, LifeCycles } from "single-spa";
import * as singleSpa from "single-spa";

console.log("🚀 Iniciando caixa-sipnc-host...");

// Expõe Single SPA globalmente para debugging
(window as any).singleSpa = singleSpa;
console.log("✅ Single SPA exposto globalmente:", !!window.singleSpa);

// Registra o navbar primeiro
console.log("📋 Registrando navbar...");
registerApplication({
    name: "caixa-sipnc-navbar",
    app: () => {
        console.log("🔄 Carregando caixa-sipnc-navbar...");
        return System.import<LifeCycles>("caixa-sipnc-navbar");
    },
    activeWhen: () => {
        console.log("✅ Navbar sempre ativo");
        return true;
    }
});



let jsonApps;
try {
    const jsonAppsString = sessionStorage.getItem("json-apps");
    if (jsonAppsString) {
        jsonApps = JSON.parse(jsonAppsString);
        console.log("Apps carregados do sessionStorage:", jsonApps);
    } else {
        console.warn("Nenhum app encontrado no sessionStorage com chave 'json-apps'");
        jsonApps = [];
    }
} catch (error) {
    console.error("Erro ao parsear json-apps do sessionStorage:", error);
    jsonApps = [];
}

for (let appl of jsonApps) {
    try {
        console.log(`📱 Registrando app: ${appl.application} para rota: ${appl.route}`);
        registerApplication({
            name: appl.application,
            app: () => {
                console.log(`🔄 Carregando ${appl.application} de ${appl.path}`);
                return System.import<LifeCycles>(appl.application)
                    .then((module) => {
                        console.log(`✅ ${appl.application} carregado com sucesso:`, module);
                        return module;
                    })
                    .catch((error) => {
                        console.error(`❌ Erro ao carregar ${appl.application}:`, error);
                        throw error;
                    });
            },
            activeWhen: (location) => {
                const isActive = ativarApp(location, appl);
                if (isActive) {
                    console.log(`✅ App ${appl.application} ativo para ${location.pathname}`);
                }
                return isActive;
            }
        });
        console.log(`✅ App registrado: ${appl.application}`);
    } catch (error) {
        console.error(`❌ Erro ao registrar app ${appl.application}:`, error);
    }
}

console.log("🚀 Iniciando Single SPA...");
start();
console.log("✅ Single SPA iniciado!");

// Debug da URL atual
console.log("🌐 URL atual:", window.location.href);
console.log("📍 Pathname atual:", window.location.pathname);

// Listener para mudanças de rota
window.addEventListener('popstate', () => {
    console.log("🔄 Mudança de rota detectada:", window.location.pathname);
});

// Força uma verificação inicial após um pequeno delay
setTimeout(() => {
    console.log("🔍 Verificação inicial de apps ativos...");
    if (window.singleSpa) {
        const appNames = window.singleSpa.getAppNames();
        const appStatuses = window.singleSpa.getAppStatuses();
        console.log("📱 Apps disponíveis:", appNames);
        console.log("📊 Status dos apps:", appStatuses);

        // Verifica quais apps deveriam estar ativos
        appNames?.forEach(appName => {
            const status = appStatuses?.[appName];
            console.log(`📋 ${appName}: ${status}`);

            // Verifica se o elemento DOM existe para o app
            const domElement = document.getElementById(`single-spa-application:${appName}`);
            console.log(`🏠 DOM element para ${appName}:`, domElement ? "✅ Existe" : "❌ Não encontrado");
        });

        // Verifica elementos DOM específicos
        console.log("🏠 Verificação de elementos DOM:");
        console.log("- Navbar DOM:", document.getElementById("single-spa-application:caixa-sipnc-navbar") ? "✅" : "❌");
        console.log("- Previdencia DOM:", document.getElementById("single-spa-application:PlataformaCaixa-PosVenda-Previdencia") ? "✅" : "❌");
        console.log("- Main content:", document.getElementById("main-content") ? "✅" : "❌");
    }
}, 2000);

function ativarApp(location: Location, appl: any) {
    let rotaAppl = appl.route.replace(/\/+$/, '').toLowerCase();
    let rotaSelecionada = location.pathname.replace(/\/+$/, '').toLowerCase();

    console.log(`🔍 Verificando ativação:`, {
        app: appl.application,
        rotaApp: rotaAppl,
        rotaAtual: rotaSelecionada,
        url: location.href
    });

    if (comparaRotas(rotaAppl, rotaSelecionada)) {
        console.log(`🎯 App ${appl.application} deve ser ativado!`);
        return true;
    }
    return false;
}

function comparaRotas(rotaApp: string, rotaLocation: string) {
    if (rotaApp == rotaLocation) {
        return true;
    } else {
        if (rotaApp.length >= rotaLocation.length) {
            return false;
        } else {
            if (rotaLocation.startsWith(rotaApp) && rotaLocation.charAt(rotaApp.length) == '/') {
                return true;
            } else {
                return false;
            }
        }
    }
}

// Debug completo do ambiente
console.log("🔧 Debug do ambiente:");
console.log("📦 SessionStorage json-apps:", sessionStorage.getItem("json-apps"));
console.log("🌐 System.js disponível:", !!window.System);
console.log("🎯 Single SPA disponível:", !!window.singleSpa);

// Verifica import maps
if (window.System) {
    console.log("📋 Import maps do System.js:");
    try {
        // @ts-ignore
        console.log(window.System.getImportMap?.() || "Import map não disponível");
    } catch (e) {
        console.log("Erro ao obter import map:", e);
    }
}

// Debug detalhado após inicialização
setTimeout(() => {
    console.log("🔍 Debug pós-inicialização:");
    console.log("Apps registrados:", window.singleSpa?.getAppNames());
    console.log("Status apps:", window.singleSpa?.getAppStatuses());

    // Testa carregamento manual de um app
    if (window.System && window.singleSpa) {
        console.log("🧪 Testando carregamento manual do navbar...");
        window.System.import('caixa-sipnc-navbar')
            .then(() => console.log("✅ Navbar carregado com sucesso"))
            .catch((err: any) => console.error("❌ Erro ao carregar navbar:", err));
    }
}, 3000);

