
import { registerApplication, start, LifeCycles, addError<PERSON>and<PERSON> } from "single-spa";
import * as singleSpa from "single-spa";

// Adiciona handler para capturar erros de mount/unmount
addErrorHandler((error) => {
    console.error("🚨 Erro no Single SPA:", error);
    console.error("📍 Detalhes do erro:", {
        appOrParcelName: error.appOrParcelName,
        errorCode: error.message,
        stack: error.stack
    });
});

console.log("🚀 Iniciando caixa-sipnc-host...");

// Expõe Single SPA globalmente para debugging
(window as any).singleSpa = singleSpa;
console.log("✅ Single SPA exposto globalmente:", !!window.singleSpa);

// Registra o navbar primeiro
console.log("📋 Registrando navbar...");
registerApplication({
    name: "caixa-sipnc-navbar",
    app: () => {
        console.log("🔄 Carregando caixa-sipnc-navbar...");
        return System.import<LifeCycles>("caixa-sipnc-navbar");
    },
    activeWhen: () => {
        console.log("✅ Navbar sempre ativo");
        return true;
    }
});



let jsonApps;
try {
    const jsonAppsString = sessionStorage.getItem("json-apps");
    if (jsonAppsString) {
        jsonApps = JSON.parse(jsonAppsString);
        console.log("Apps carregados do sessionStorage:", jsonApps);
    } else {
        console.warn("Nenhum app encontrado no sessionStorage com chave 'json-apps'");
        jsonApps = [];
    }
} catch (error) {
    console.error("Erro ao parsear json-apps do sessionStorage:", error);
    jsonApps = [];
}

for (let appl of jsonApps) {
    try {
        console.log(`📱 Registrando app: ${appl.application} para rota: ${appl.route}`);
        registerApplication({
            name: appl.application,
            app: () => {
                console.log(`🔄 Carregando ${appl.application} de ${appl.path}`);
                return System.import<LifeCycles>(appl.application)
                    .then((module) => {
                        console.log(`✅ ${appl.application} carregado com sucesso:`, module);

                        // Verifica se o módulo tem as funções necessárias
                        if (typeof module.mount !== 'function') {
                            console.error(`❌ ${appl.application} não tem função mount!`);
                        }
                        if (typeof module.unmount !== 'function') {
                            console.error(`❌ ${appl.application} não tem função unmount!`);
                        }
                        if (typeof module.bootstrap === 'function') {
                            console.log(`✅ ${appl.application} tem função bootstrap`);
                        }

                        return module;
                    })
                    .catch((error) => {
                        console.error(`❌ Erro ao carregar ${appl.application}:`, error);
                        throw error;
                    });
            },
            activeWhen: (location) => {
                const isActive = ativarApp(location, appl);
                if (isActive) {
                    console.log(`✅ App ${appl.application} ativo para ${location.pathname}`);

                    // Debug adicional quando app está ativo
                    setTimeout(() => {
                        const domElement = document.getElementById(`single-spa-application:${appl.application}`);
                        console.log(`🏠 DOM element para ${appl.application}:`, domElement ? "✅ Existe" : "❌ Não encontrado");

                        if (domElement) {
                            console.log(`📏 Dimensões do elemento:`, {
                                width: domElement.offsetWidth,
                                height: domElement.offsetHeight,
                                innerHTML: domElement.innerHTML.length > 0 ? "Tem conteúdo" : "Vazio"
                            });
                        }
                    }, 1000);
                }
                return isActive;
            }
        });
        console.log(`✅ App registrado: ${appl.application}`);
    } catch (error) {
        console.error(`❌ Erro ao registrar app ${appl.application}:`, error);
    }
}

console.log("🚀 Iniciando Single SPA...");

// Aumenta timeout para MFEs que demoram para carregar
import { setMountMaxTime, setUnmountMaxTime, setBootstrapMaxTime } from "single-spa";
setMountMaxTime(10000); // 10 segundos para mount
setUnmountMaxTime(5000); // 5 segundos para unmount
setBootstrapMaxTime(5000); // 5 segundos para bootstrap

start();
console.log("✅ Single SPA iniciado!");

// Verifica se o Single SPA foi iniciado corretamente
setTimeout(() => {
    console.log("🔍 Verificação pós-start:");
    console.log("- Single SPA iniciado:", (window.singleSpa as any).isStarted?.() || "método não disponível");
    console.log("- Apps registrados:", window.singleSpa?.getAppNames()?.length || 0);
}, 500);

// Debug da URL atual
console.log("🌐 URL atual:", window.location.href);
console.log("📍 Pathname atual:", window.location.pathname);

// Listener para mudanças de rota
window.addEventListener('popstate', () => {
    console.log("🔄 Mudança de rota detectada:", window.location.pathname);
});

// Força uma verificação inicial após um pequeno delay
setTimeout(() => {
    console.log("🔍 Verificação inicial de apps ativos...");
    if (window.singleSpa) {
        console.log("🔧 Métodos disponíveis no Single SPA:", Object.keys(window.singleSpa));

        try {
            const appNames = window.singleSpa.getAppNames();
            console.log("📱 Apps disponíveis:", appNames);

            // Tenta usar getAppStatuses se disponível
            if (typeof window.singleSpa.getAppStatuses === 'function') {
                const appStatuses = window.singleSpa.getAppStatuses();
                console.log("📊 Status dos apps:", appStatuses);

                // Verifica quais apps deveriam estar ativos
                appNames?.forEach(appName => {
                    const status = appStatuses?.[appName];
                    console.log(`📋 ${appName}: ${status}`);
                });
            } else {
                console.log("⚠️ getAppStatuses não disponível nesta versão do Single SPA");

                // Usa método alternativo para verificar status
                appNames?.forEach(appName => {
                    try {
                        const status = (window.singleSpa as any).getAppStatus?.(appName) || 'unknown';
                        console.log(`📋 ${appName}: ${status}`);
                    } catch (e) {
                        console.log(`📋 ${appName}: status não disponível`);
                    }
                });
            }

            // Verifica se o elemento DOM existe para cada app
            appNames?.forEach(appName => {
                const domElement = document.getElementById(`single-spa-application:${appName}`);
                console.log(`🏠 DOM element para ${appName}:`, domElement ? "✅ Existe" : "❌ Não encontrado");
            });

        } catch (error) {
            console.error("❌ Erro ao verificar apps:", error);
        }

        // Verifica elementos DOM específicos
        console.log("🏠 Verificação de elementos DOM:");
        console.log("- Navbar DOM:", document.getElementById("single-spa-application:caixa-sipnc-navbar") ? "✅" : "❌");
        console.log("- Previdencia DOM:", document.getElementById("single-spa-application:PlataformaCaixa-PosVenda-Previdencia") ? "✅" : "❌");
        console.log("- Main content:", document.getElementById("main-content") ? "✅" : "❌");
    }
}, 2000);

function ativarApp(location: Location, appl: any) {
    let rotaAppl = appl.route.replace(/\/+$/, '').toLowerCase();
    let rotaSelecionada = location.pathname.replace(/\/+$/, '').toLowerCase();

    console.log(`🔍 Verificando ativação:`, {
        app: appl.application,
        rotaApp: rotaAppl,
        rotaAtual: rotaSelecionada,
        url: location.href
    });

    if (comparaRotas(rotaAppl, rotaSelecionada)) {
        console.log(`🎯 App ${appl.application} deve ser ativado!`);
        return true;
    }
    return false;
}

function comparaRotas(rotaApp: string, rotaLocation: string) {
    if (rotaApp == rotaLocation) {
        return true;
    } else {
        if (rotaApp.length >= rotaLocation.length) {
            return false;
        } else {
            if (rotaLocation.startsWith(rotaApp) && rotaLocation.charAt(rotaApp.length) == '/') {
                return true;
            } else {
                return false;
            }
        }
    }
}

// Debug completo do ambiente
console.log("🔧 Debug do ambiente:");
console.log("📦 SessionStorage json-apps:", sessionStorage.getItem("json-apps"));
console.log("🌐 System.js disponível:", !!window.System);
console.log("🎯 Single SPA disponível:", !!window.singleSpa);

// Verifica import maps
if (window.System) {
    console.log("📋 Import maps do System.js:");
    try {
        // @ts-ignore
        console.log(window.System.getImportMap?.() || "Import map não disponível");
    } catch (e) {
        console.log("Erro ao obter import map:", e);
    }
}

// Debug detalhado após inicialização
setTimeout(() => {
    console.log("🔍 Debug pós-inicialização:");

    try {
        console.log("Apps registrados:", window.singleSpa?.getAppNames());

        // Verifica se getAppStatuses existe antes de usar
        if (window.singleSpa && typeof window.singleSpa.getAppStatuses === 'function') {
            console.log("Status apps:", window.singleSpa.getAppStatuses());
        } else {
            console.log("⚠️ getAppStatuses não disponível - usando método alternativo");
            const appNames = window.singleSpa?.getAppNames();
            if (appNames) {
                const statuses: Record<string, string> = {};
                appNames.forEach(name => {
                    try {
                        statuses[name] = (window.singleSpa as any).getAppStatus?.(name) || 'unknown';
                    } catch (e) {
                        statuses[name] = 'error';
                    }
                });
                console.log("Status apps (alternativo):", statuses);
            }
        }
    } catch (error) {
        console.error("❌ Erro no debug pós-inicialização:", error);
    }

    // Testa carregamento manual de um app
    if (window.System && window.singleSpa) {
        console.log("🧪 Testando carregamento manual do navbar...");
        window.System.import('caixa-sipnc-navbar')
            .then(() => console.log("✅ Navbar carregado com sucesso"))
            .catch((err: any) => console.error("❌ Erro ao carregar navbar:", err));

        // Testa carregamento do MFE de previdência
        console.log("🧪 Testando carregamento manual do MFE Previdência...");
        window.System.import('PlataformaCaixa-PosVenda-Previdencia')
            .then(() => console.log("✅ MFE Previdência carregado com sucesso"))
            .catch((err: any) => console.error("❌ Erro ao carregar MFE Previdência:", err));

        // Testa se a URL do MFE está acessível
        console.log("🌐 Testando conectividade com MFE...");
        fetch('http://localhost:4002/CVP-PlataformaCaixa-PosVenda-Previdencia.js')
            .then(response => {
                if (response.ok) {
                    console.log("✅ MFE acessível na porta 4002");
                    return response.text();
                } else {
                    console.error("❌ MFE não acessível - Status:", response.status);
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(content => {
                console.log("📄 Conteúdo do MFE (primeiros 200 chars):", content.substring(0, 200));

                // Verifica se é um MFE válido do Single SPA
                if (content.includes('mount') && content.includes('unmount')) {
                    console.log("✅ MFE parece ser válido (contém mount/unmount)");
                } else {
                    console.warn("⚠️ MFE pode não ser válido (não contém mount/unmount)");
                }
            })
            .catch((err: any) => console.error("❌ Erro de conectividade com MFE:", err));
    }
}, 3000);

