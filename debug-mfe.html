<!DOCTYPE html>
<html>
<head>
    <title>Debug MFE Loading</title>
</head>
<body>
    <h1>🔍 Debug MFE Loading</h1>
    
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function log(message) {
            console.log(message);
            results.innerHTML += `<p>${message}</p>`;
        }
        
        // Teste 1: Verificar se o MFE está acessível diretamente
        log("🧪 Teste 1: Verificando MFE direto na porta 4002...");
        fetch('http://localhost:4002/CVP-PlataformaCaixa-PosVenda-Previdencia.js')
            .then(response => {
                if (response.ok) {
                    log("✅ MFE acessível diretamente na porta 4002");
                    return response.text();
                } else {
                    log(`❌ MFE não acessível - Status: ${response.status}`);
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(content => {
                log(`📄 Tamanho do conteúdo: ${content.length} chars`);
                if (content.includes('mount') && content.includes('unmount')) {
                    log("✅ MFE parece ser válido (contém mount/unmount)");
                } else {
                    log("⚠️ MFE pode não ser válido (não contém mount/unmount)");
                }
            })
            .catch(err => log(`❌ Erro ao acessar MFE direto: ${err.message}`));
        
        // Teste 2: Verificar se o proxy está funcionando
        log("🧪 Teste 2: Verificando MFE através do proxy...");
        fetch('http://localhost:9000/mfe-proxy/previdencia/CVP-PlataformaCaixa-PosVenda-Previdencia.js')
            .then(response => {
                if (response.ok) {
                    log("✅ MFE acessível através do proxy");
                    return response.text();
                } else {
                    log(`❌ MFE não acessível via proxy - Status: ${response.status}`);
                    throw new Error(`HTTP ${response.status}`);
                }
            })
            .then(content => {
                log(`📄 Tamanho do conteúdo via proxy: ${content.length} chars`);
            })
            .catch(err => log(`❌ Erro ao acessar MFE via proxy: ${err.message}`));
        
        // Teste 3: Verificar configuração do ambiente
        log("🧪 Teste 3: Verificando configuração...");
        setTimeout(() => {
            const jsonApps = sessionStorage.getItem("json-apps");
            if (jsonApps) {
                log("✅ SessionStorage json-apps encontrado");
                try {
                    const apps = JSON.parse(jsonApps);
                    log(`📱 Número de apps: ${apps.length}`);
                    apps.forEach(app => {
                        log(`📋 App: ${app.application} - Rota: ${app.route} - Path: ${app.path}`);
                    });
                } catch (e) {
                    log(`❌ Erro ao parsear json-apps: ${e.message}`);
                }
            } else {
                log("❌ SessionStorage json-apps não encontrado");
            }
            
            // Verificar Single SPA
            if (window.singleSpa) {
                log("✅ Single SPA disponível");
                try {
                    const appNames = window.singleSpa.getAppNames();
                    log(`📱 Apps registrados no Single SPA: ${appNames.join(', ')}`);
                } catch (e) {
                    log(`❌ Erro ao obter apps do Single SPA: ${e.message}`);
                }
            } else {
                log("❌ Single SPA não disponível");
            }
        }, 2000);
    </script>
</body>
</html>
